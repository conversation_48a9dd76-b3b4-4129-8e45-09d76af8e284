"""
Comprehensive unit tests for Azure Blob Storage sink module.

This module tests the AzureBlobStorageSink class including connection handling,
log buffering, upload functionality, error handling, and threading behavior.
"""

import threading
from io import StringIO
from unittest.mock import MagicMock, Mock, patch

import pytest

from autolodge.log_utils.azure_sink import AzureBlobStorageSink


class TestAzureBlobStorageSink:
    """Test cases for AzureBlobStorageSink class."""

    def test_init_successful(self, mock_blob_service_client):
        """Test successful initialization of Azure Blob Storage sink."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        assert sink.connection_string == 'test_connection_string'
        assert sink.container_name == 'test-container'
        assert sink.blob_prefix == 'test'
        assert sink.environment == 'TEST'
        assert sink.blob_service_client is not None
        assert sink.current_blob_name is not None
        assert 'test_TEST_' in sink.current_blob_name
        assert sink.current_blob_name.endswith('.log')
        assert sink.log_buffer == []
        assert sink.upload_interval == 30
        assert sink.max_buffer_size == 15

    def test_init_with_blob_client_creation_failure(self):
        """Test initialization failure when blob client creation fails."""
        with patch('autolodge.log_utils.azure_sink.BlobServiceClient', side_effect=Exception('Connection failed')):
            with pytest.raises(Exception, match='Connection failed'):
                AzureBlobStorageSink(
                    connection_string='invalid_connection_string',
                    container_name='test-container',
                    blob_prefix='test',
                    environment='TEST',
                )

    @patch('autolodge.log_utils.azure_sink.logger')
    def test_init_logging_messages(self, mock_logger, mock_blob_service_client):
        """Test that initialization logs appropriate messages."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Check that success message was logged
        mock_logger.info.assert_called()
        logged_message = mock_logger.info.call_args[0][0]
        assert '☁️ Azure Blob Storage logging initialized' in logged_message
        assert 'test-container' in logged_message
        assert 'TEST' in logged_message

    def test_blob_name_generation(self, mock_blob_service_client):
        """Test that blob names are generated with correct format."""
        with patch('autolodge.log_utils.azure_sink.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            sink = AzureBlobStorageSink(
                connection_string='test_connection_string',
                container_name='test-container',
                blob_prefix='score',
                environment='PROD',
            )

            assert sink.current_blob_name == 'score_PROD_2024-01-15_10-30-45.log'

    def test_write_message_to_buffer(self, mock_blob_service_client):
        """Test writing log messages to buffer."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Create mock message record
        mock_record = {
            'time': Mock(),
            'level': Mock(),
            'name': 'test_module',
            'function': 'test_function',
            'line': 123,
            'message': 'Test log message',
        }
        mock_record['time'].strftime = Mock(return_value='2024-01-15 10:30:45')
        mock_record['level'].__format__ = Mock(return_value='INFO    ')

        mock_message = Mock()
        mock_message.record = mock_record

        # Mock the format method
        with patch('autolodge.log_utils.azure_sink.AzureBlobStorageSink._upload_buffer'):
            sink.write(mock_message)

            assert len(sink.log_buffer) == 1
            assert 'Test log message' in sink.log_buffer[0]

    def test_write_triggers_upload_when_buffer_full(self, mock_blob_service_client):
        """Test that write triggers upload when buffer reaches max size."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Set small buffer size for testing
        sink.max_buffer_size = 2

        # Create mock message
        mock_record = {
            'time': Mock(),
            'level': Mock(),
            'name': 'test',
            'function': 'test',
            'line': 1,
            'message': 'Test',
        }
        mock_record['time'].strftime = Mock(return_value='2024-01-15 10:30:45')
        mock_record['level'].__format__ = Mock(return_value='INFO    ')

        mock_message = Mock()
        mock_message.record = mock_record

        with patch.object(sink, '_upload_buffer') as mock_upload:
            # First message should not trigger upload
            sink.write(mock_message)
            mock_upload.assert_not_called()

            # Second message should trigger upload
            sink.write(mock_message)
            mock_upload.assert_called_once()

    def test_upload_buffer_successful(self, mock_blob_service_client):
        """Test successful buffer upload to Azure Blob Storage."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Add test data to buffer
        sink.log_buffer = ['Log entry 1', 'Log entry 2', 'Log entry 3']

        # Mock blob client
        mock_blob_client = MagicMock()
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify upload was called
        mock_blob_client.upload_blob.assert_called_once()
        upload_data = mock_blob_client.upload_blob.call_args[1]['data']
        assert 'Log entry 1' in upload_data
        assert 'Log entry 2' in upload_data
        assert 'Log entry 3' in upload_data

        # Verify buffer was cleared
        assert sink.log_buffer == []

    @patch('autolodge.log_utils.azure_sink.logger')
    def test_upload_buffer_failure(self, mock_logger, mock_blob_service_client):
        """Test buffer upload failure handling."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Add test data to buffer
        sink.log_buffer = ['Log entry 1']

        # Mock blob client to raise exception
        mock_blob_client = MagicMock()
        mock_blob_client.upload_blob.side_effect = Exception('Upload failed')
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify error was logged
        mock_logger.error.assert_called()
        error_message = mock_logger.error.call_args[0][0]
        assert '❌ Failed to upload logs to Azure Blob Storage' in error_message
        assert 'Upload failed' in error_message

    def test_upload_buffer_empty_buffer(self, mock_blob_service_client):
        """Test that upload_buffer handles empty buffer gracefully."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Ensure buffer is empty
        sink.log_buffer = []

        # Mock blob client
        mock_blob_client = MagicMock()
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify upload was not called
        mock_blob_client.upload_blob.assert_not_called()

    def test_upload_buffer_no_blob_client(self, mock_blob_service_client):
        """Test upload_buffer when blob client is None."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Add test data and set blob client to None
        sink.log_buffer = ['Log entry 1']
        sink.blob_service_client = None

        # Should not raise exception
        sink._upload_buffer()

        # Buffer should remain unchanged
        assert sink.log_buffer == ['Log entry 1']

    def test_upload_buffer_no_blob_name(self, mock_blob_service_client):
        """Test upload_buffer when current_blob_name is None."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Add test data and set blob name to None
        sink.log_buffer = ['Log entry 1']
        sink.current_blob_name = None

        # Should not raise exception
        sink._upload_buffer()

        # Buffer should remain unchanged
        assert sink.log_buffer == ['Log entry 1']

    def test_write_error_handling(self, mock_blob_service_client):
        """Test error handling in write method."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Create mock message that will cause an error
        mock_message = Mock()
        mock_message.record = {'invalid': 'record'}

        # Capture stderr
        captured_output = StringIO()

        with patch('sys.stderr', captured_output):
            sink.write(mock_message)

            # Check that error was written to stderr
            error_output = captured_output.getvalue()
            assert '❌ Azure Blob Storage sink error' in error_output

    def test_background_upload_thread_creation(self, mock_blob_service_client):
        """Test that background upload thread is created during initialization."""
        with patch('threading.Thread') as mock_thread:
            mock_thread_instance = MagicMock()
            mock_thread.return_value = mock_thread_instance

            sink = AzureBlobStorageSink(
                connection_string='test_connection_string',
                container_name='test-container',
                blob_prefix='test',
                environment='TEST',
            )

            # Verify thread was created and started
            mock_thread.assert_called_once()
            mock_thread_instance.start.assert_called_once()

            # Verify thread is daemon
            assert mock_thread.call_args[1]['daemon'] is True

    def test_message_formatting(self, mock_blob_service_client):
        """Test that log messages are formatted correctly."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Create detailed mock record
        mock_time = Mock()
        mock_time.strftime = Mock(return_value='2024-01-15 10:30:45')

        mock_level = Mock()
        mock_level.__format__ = Mock(return_value='INFO    ')

        mock_record = {
            'time': mock_time,
            'level': mock_level,
            'name': 'test_module',
            'function': 'test_function',
            'line': 123,
            'message': 'Test log message',
        }

        mock_message = Mock()
        mock_message.record = mock_record

        with patch.object(sink, '_upload_buffer'):
            sink.write(mock_message)

            assert len(sink.log_buffer) == 1
            formatted_message = sink.log_buffer[0]

            # Check format components
            assert '2024-01-15 10:30:45' in formatted_message
            assert 'INFO    ' in formatted_message
            assert 'test_module:test_function:123' in formatted_message
            assert 'Test log message' in formatted_message

    def test_thread_safety_with_concurrent_writes(self, mock_blob_service_client):
        """Test thread safety with concurrent write operations."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Set high buffer size to prevent automatic uploads
        sink.max_buffer_size = 1000

        # Create mock message
        mock_record = {
            'time': Mock(),
            'level': Mock(),
            'name': 'test',
            'function': 'test',
            'line': 1,
            'message': 'Test',
        }
        mock_record['time'].strftime = Mock(return_value='2024-01-15 10:30:45')
        mock_record['level'].__format__ = Mock(return_value='INFO    ')

        mock_message = Mock()
        mock_message.record = mock_record

        # Function to write messages concurrently
        def write_messages():
            for i in range(10):
                sink.write(mock_message)

        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=write_messages)
            threads.append(thread)

        # Start all threads
        for thread in threads:
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify all messages were added (5 threads * 10 messages each)
        assert len(sink.log_buffer) == 50


class TestAzureBlobStorageSinkErrorScenarios:
    """Test cases for error scenarios and edge cases."""

    def test_connection_timeout_simulation(self):
        """Test handling of connection timeout scenarios."""
        with patch('autolodge.log_utils.azure_sink.BlobServiceClient') as mock_client_class:
            # Simulate timeout during client creation
            mock_client_class.side_effect = Exception('Connection timeout')

            with pytest.raises(Exception, match='Connection timeout'):
                AzureBlobStorageSink(
                    connection_string='test_connection_string',
                    container_name='test-container',
                    blob_prefix='test',
                    environment='TEST',
                )

    def test_invalid_connection_string_format(self):
        """Test handling of invalid connection string format."""
        with patch('autolodge.log_utils.azure_sink.BlobServiceClient') as mock_client_class:
            mock_client_class.side_effect = ValueError('Invalid connection string format')

            with pytest.raises(ValueError, match='Invalid connection string format'):
                AzureBlobStorageSink(
                    connection_string='invalid_format',
                    container_name='test-container',
                    blob_prefix='test',
                    environment='TEST',
                )

    @patch('autolodge.log_utils.azure_sink.logger')
    def test_storage_quota_exceeded(self, mock_logger, mock_blob_service_client):
        """Test handling of storage quota exceeded scenarios."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        sink.log_buffer = ['Test log entry']

        # Mock quota exceeded error
        mock_blob_client = MagicMock()
        mock_blob_client.upload_blob.side_effect = Exception('Storage quota exceeded')
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify error was logged
        mock_logger.error.assert_called()
        error_message = mock_logger.error.call_args[0][0]
        assert 'Storage quota exceeded' in error_message

    @patch('autolodge.log_utils.azure_sink.logger')
    def test_permission_denied_scenario(self, mock_logger, mock_blob_service_client):
        """Test handling of permission denied scenarios."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        sink.log_buffer = ['Test log entry']

        # Mock permission denied error
        mock_blob_client = MagicMock()
        mock_blob_client.upload_blob.side_effect = Exception('Permission denied')
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify error was logged
        mock_logger.error.assert_called()
        error_message = mock_logger.error.call_args[0][0]
        assert 'Permission denied' in error_message

    def test_invalid_container_name_handling(self):
        """Test handling of invalid container names."""
        with patch('autolodge.log_utils.azure_sink.BlobServiceClient') as mock_client_class:
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client

            # Mock container creation failure
            mock_client.get_container_client.side_effect = ValueError('Invalid container name')

            with pytest.raises(ValueError, match='Invalid container name'):
                sink = AzureBlobStorageSink(
                    connection_string='test_connection_string',
                    container_name='Invalid-Container-Name!',
                    blob_prefix='test',
                    environment='TEST',
                )

    def test_network_connectivity_issues(self, mock_blob_service_client):
        """Test handling of network connectivity issues during upload."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        sink.log_buffer = ['Test log entry']

        # Mock network error
        mock_blob_client = MagicMock()
        mock_blob_client.upload_blob.side_effect = ConnectionError('Network unreachable')
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        # Should not raise exception, should handle gracefully
        sink._upload_buffer()

        # Buffer should remain unchanged due to upload failure
        assert sink.log_buffer == ['Test log entry']


class TestAzureBlobStorageSinkChunkUpload:
    """Test cases for chunk-based upload functionality."""

    def test_large_log_buffer_upload(self, mock_blob_service_client):
        """Test upload of large log buffers."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Create large buffer
        large_buffer = [f'Log entry {i}' for i in range(1000)]
        sink.log_buffer = large_buffer

        # Mock blob client
        mock_blob_client = MagicMock()
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify upload was called with all data
        mock_blob_client.upload_blob.assert_called_once()
        upload_data = mock_blob_client.upload_blob.call_args[1]['data']

        # Check that all entries are in the upload data
        for i in range(1000):
            assert f'Log entry {i}' in upload_data

    def test_upload_with_overwrite_behavior(self, mock_blob_service_client):
        """Test that uploads use overwrite=True to handle existing blobs."""
        sink = AzureBlobStorageSink(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        sink.log_buffer = ['Test log entry']

        # Mock blob client
        mock_blob_client = MagicMock()
        sink.blob_service_client.get_container_client.return_value.get_blob_client.return_value = mock_blob_client

        sink._upload_buffer()

        # Verify upload was called with overwrite=True
        mock_blob_client.upload_blob.assert_called_once()
        call_kwargs = mock_blob_client.upload_blob.call_args[1]
        assert call_kwargs['overwrite'] is True


class TestAzureBlobStorageSinkNamingConventions:
    """Test cases for blob naming conventions."""

    def test_environment_specific_naming_prod(self, mock_blob_service_client):
        """Test blob naming for PROD environment."""
        with patch('autolodge.log_utils.azure_sink.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            sink = AzureBlobStorageSink(
                connection_string='test_connection_string',
                container_name='test-container',
                blob_prefix='scoring_logs',
                environment='PROD',
            )

            expected_name = 'scoring_logs_PROD_2024-01-15_10-30-45.log'
            assert sink.current_blob_name == expected_name

    def test_environment_specific_naming_uat(self, mock_blob_service_client):
        """Test blob naming for UAT environment."""
        with patch('autolodge.log_utils.azure_sink.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            sink = AzureBlobStorageSink(
                connection_string='test_connection_string',
                container_name='test-container',
                blob_prefix='scoring_logs',
                environment='UAT',
            )

            expected_name = 'scoring_logs_UAT_2024-01-15_10-30-45.log'
            assert sink.current_blob_name == expected_name

    def test_environment_specific_naming_dev(self, mock_blob_service_client):
        """Test blob naming for DEV environment."""
        with patch('autolodge.log_utils.azure_sink.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            sink = AzureBlobStorageSink(
                connection_string='test_connection_string',
                container_name='test-container',
                blob_prefix='scoring_logs',
                environment='DEV',
            )

            expected_name = 'scoring_logs_DEV_2024-01-15_10-30-45.log'
            assert sink.current_blob_name == expected_name

    def test_default_naming_when_environment_unknown(self, mock_blob_service_client):
        """Test blob naming when environment is unknown."""
        with patch('autolodge.log_utils.azure_sink.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            sink = AzureBlobStorageSink(
                connection_string='test_connection_string',
                container_name='test-container',
                blob_prefix='scoring_logs',
                environment='UNKNOWN',
            )

            expected_name = 'scoring_logs_UNKNOWN_2024-01-15_10-30-45.log'
            assert sink.current_blob_name == expected_name
