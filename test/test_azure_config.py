"""
Comprehensive unit tests for Azure Blob Storage configuration module.

This module tests the AzureLoggingConfig class including environment variable loading,
environment detection, connection string handling, and configuration validation.
"""

import os
from unittest.mock import patch

from autolodge.log_utils.config import AzureLoggingConfig, check_azure_dependencies, get_azure_logging_config


class TestAzureLoggingConfig:
    """Test cases for AzureLoggingConfig class."""

    def test_init_with_defaults(self, clean_env):
        """Test initialization with default values when no environment variables are set."""
        config = AzureLoggingConfig()

        assert config.connection_string is None
        assert config.storage_account is None
        assert config.storage_key is None
        assert config.container_name == 'autolodge-logs'
        assert config.blob_prefix == 'score'
        assert config.environment == 'LOCAL'

    def test_init_with_environment_variables(self, azure_env_vars):
        """Test initialization with environment variables."""
        config = AzureLoggingConfig()

        assert config.connection_string == azure_env_vars['AZURE_STORAGE_CONNECTION_STRING']
        assert config.storage_account == azure_env_vars['AZURE_STORAGE_ACCOUNT_NAME']
        assert config.storage_key == azure_env_vars['AZURE_STORAGE_ACCOUNT_KEY']
        assert config.container_name == azure_env_vars['AZURE_LOG_CONTAINER_NAME']
        assert config.blob_prefix == azure_env_vars['AZURE_LOG_BLOB_PREFIX']
        assert config.environment == azure_env_vars['DEPLOYMENT_ENVIRONMENT']

    def test_get_connection_string_from_env_var(self, azure_env_vars):
        """Test getting connection string from environment variable."""
        config = AzureLoggingConfig()

        connection_string = config.get_connection_string()
        assert connection_string == azure_env_vars['AZURE_STORAGE_CONNECTION_STRING']

    def test_get_connection_string_from_account_and_key(self, clean_env):
        """Test constructing connection string from account name and key."""
        with patch.dict(
            os.environ, {'AZURE_STORAGE_ACCOUNT_NAME': 'testaccount', 'AZURE_STORAGE_ACCOUNT_KEY': 'testkey'}
        ):
            config = AzureLoggingConfig()

            connection_string = config.get_connection_string()
            expected = 'DefaultEndpointsProtocol=https;AccountName=testaccount;AccountKey=testkey;EndpointSuffix=core.windows.net'
            assert connection_string == expected

    def test_get_connection_string_none_when_not_configured(self, clean_env):
        """Test that get_connection_string returns None when not configured."""
        config = AzureLoggingConfig()

        connection_string = config.get_connection_string()
        assert connection_string is None

    def test_is_configured_true_with_connection_string(self, azure_env_vars):
        """Test is_configured returns True when connection string is available."""
        config = AzureLoggingConfig()

        assert config.is_configured() is True

    def test_is_configured_true_with_account_and_key(self, clean_env):
        """Test is_configured returns True when account name and key are available."""
        with patch.dict(
            os.environ, {'AZURE_STORAGE_ACCOUNT_NAME': 'testaccount', 'AZURE_STORAGE_ACCOUNT_KEY': 'testkey'}
        ):
            config = AzureLoggingConfig()

            assert config.is_configured() is True

    def test_is_configured_false_when_not_configured(self, clean_env):
        """Test is_configured returns False when not configured."""
        config = AzureLoggingConfig()

        assert config.is_configured() is False

    def test_environment_detection_direct_deployment_environment(self, clean_env):
        """Test environment detection from DEPLOYMENT_ENVIRONMENT variable."""
        with patch.dict(os.environ, {'DEPLOYMENT_ENVIRONMENT': 'production'}):
            config = AzureLoggingConfig()

            assert config.environment == 'PRODUCTION'

    def test_environment_detection_direct_app_environment(self, clean_env):
        """Test environment detection from APP_ENVIRONMENT variable."""
        with patch.dict(os.environ, {'APP_ENVIRONMENT': 'uat'}):
            config = AzureLoggingConfig()

            assert config.environment == 'UAT'

    def test_environment_detection_from_workspace_name(self, clean_env):
        """Test environment detection from Azure ML workspace name."""
        with patch.dict(os.environ, {'AZURE_ML_WORKSPACE_NAME': 'my-prod-workspace'}):
            config = AzureLoggingConfig()

            assert config.environment == 'PROD'

    def test_environment_detection_from_model_name(self, clean_env):
        """Test environment detection from model name."""
        with patch.dict(os.environ, {'MODEL_NAME': 'autolodge-dev-model'}):
            config = AzureLoggingConfig()

            assert config.environment == 'DEV'

    def test_environment_detection_fallback_to_local(self, clean_env):
        """Test environment detection fallback to LOCAL when no patterns match."""
        config = AzureLoggingConfig()

        assert config.environment == 'LOCAL'

    def test_extract_environment_from_name_patterns(self):
        """Test environment extraction from various name patterns."""
        config = AzureLoggingConfig()

        # Test production patterns
        assert config._extract_environment_from_name('my-prod-workspace') == 'PROD'
        assert config._extract_environment_from_name('production-env') == 'PROD'
        assert config._extract_environment_from_name('prd-system') == 'PROD'

        # Test UAT patterns
        assert config._extract_environment_from_name('uat-workspace') == 'UAT'
        assert config._extract_environment_from_name('user-acceptance-test') == 'UAT'

        # Test development patterns
        assert config._extract_environment_from_name('dev-environment') == 'DEV'
        assert config._extract_environment_from_name('development-workspace') == 'DEV'

        # Test test patterns
        assert config._extract_environment_from_name('test-workspace') == 'TEST'
        assert config._extract_environment_from_name('testing-env') == 'TEST'

        # Test no match
        assert config._extract_environment_from_name('unknown-workspace') is None

    @patch('autolodge.log_utils.config.logger')
    def test_log_configuration_status_with_connection_string(self, mock_logger, azure_env_vars):
        """Test logging configuration status with connection string."""
        config = AzureLoggingConfig()
        config.log_configuration_status()

        mock_logger.info.assert_any_call('🔧 Loaded Azure Storage connection string from environment variables')
        mock_logger.info.assert_any_call(f'🔧 Azure Blob Storage container: {config.container_name}')
        mock_logger.info.assert_any_call(f'🔧 Azure Blob Storage prefix: {config.blob_prefix}')
        mock_logger.info.assert_any_call(f'🔧 Deployment environment: {config.environment}')

    @patch('autolodge.log_utils.config.logger')
    def test_log_configuration_status_with_account_credentials(self, mock_logger, clean_env):
        """Test logging configuration status with account name and key."""
        with patch.dict(
            os.environ, {'AZURE_STORAGE_ACCOUNT_NAME': 'testaccount', 'AZURE_STORAGE_ACCOUNT_KEY': 'testkey'}
        ):
            config = AzureLoggingConfig()
            config.log_configuration_status()

            mock_logger.info.assert_any_call('🔧 Loaded Azure Storage account credentials from environment variables')

    @patch('autolodge.log_utils.config.logger')
    def test_log_configuration_hints_when_not_configured(self, mock_logger, clean_env):
        """Test logging configuration hints when Azure is not configured."""
        config = AzureLoggingConfig()
        config.log_configuration_hints()

        mock_logger.warning.assert_called_with('⚠️ Azure Storage connection string not configured')
        mock_logger.info.assert_called_with(
            '💡 Set AZURE_STORAGE_CONNECTION_STRING or (AZURE_STORAGE_ACCOUNT_NAME + AZURE_STORAGE_ACCOUNT_KEY) environment variables to enable Azure Blob Storage logging'
        )

    @patch('autolodge.log_utils.config.logger')
    def test_log_configuration_hints_when_configured(self, mock_logger, azure_env_vars):
        """Test that configuration hints are not logged when Azure is configured."""
        config = AzureLoggingConfig()
        config.log_configuration_hints()

        # Should not log warning or hints when properly configured
        mock_logger.warning.assert_not_called()


class TestModuleFunctions:
    """Test cases for module-level functions."""

    def test_get_azure_logging_config(self, azure_env_vars):
        """Test get_azure_logging_config function."""
        config = get_azure_logging_config()

        assert isinstance(config, AzureLoggingConfig)
        assert config.is_configured() is True

    def test_check_azure_dependencies_available(self):
        """Test check_azure_dependencies when dependencies are available."""
        # Since the dependencies are actually available in the test environment,
        # this should return True
        result = check_azure_dependencies()

        assert result is True

    def test_check_azure_dependencies_unavailable(self):
        """Test check_azure_dependencies when dependencies are not available."""
        # Mock the import to raise ImportError
        import sys

        original_modules = sys.modules.copy()

        # Remove azure modules from sys.modules to simulate ImportError
        modules_to_remove = [key for key in sys.modules.keys() if key.startswith('azure')]
        for module in modules_to_remove:
            del sys.modules[module]

        # Mock the import to raise ImportError
        with patch.dict('sys.modules', {'azure.storage.blob': None}):
            with patch('builtins.__import__', side_effect=ImportError('No module named azure')):
                result = check_azure_dependencies()

                assert result is False

        # Restore original modules
        sys.modules.update(original_modules)


class TestEnvironmentVariableHandling:
    """Test cases for environment variable handling edge cases."""

    def test_custom_container_name(self, clean_env):
        """Test custom container name from environment variable."""
        with patch.dict(os.environ, {'AZURE_LOG_CONTAINER_NAME': 'custom-logs'}):
            config = AzureLoggingConfig()

            assert config.container_name == 'custom-logs'

    def test_custom_blob_prefix(self, clean_env):
        """Test custom blob prefix from environment variable."""
        with patch.dict(os.environ, {'AZURE_LOG_BLOB_PREFIX': 'custom'}):
            config = AzureLoggingConfig()

            assert config.blob_prefix == 'custom'

    def test_environment_case_insensitive_detection(self, clean_env):
        """Test that environment detection is case insensitive."""
        with patch.dict(os.environ, {'DEPLOYMENT_ENVIRONMENT': 'PrOd'}):
            config = AzureLoggingConfig()

            assert config.environment == 'PROD'

    def test_priority_of_environment_variables(self, clean_env):
        """Test priority order of environment variable detection."""
        # DEPLOYMENT_ENVIRONMENT should take priority over workspace name
        with patch.dict(os.environ, {'DEPLOYMENT_ENVIRONMENT': 'PROD', 'AZURE_ML_WORKSPACE_NAME': 'dev-workspace'}):
            config = AzureLoggingConfig()

            assert config.environment == 'PROD'

    def test_workspace_name_priority_over_model_name(self, clean_env):
        """Test that workspace name takes priority over model name."""
        with patch.dict(os.environ, {'AZURE_ML_WORKSPACE_NAME': 'prod-workspace', 'MODEL_NAME': 'dev-model'}):
            config = AzureLoggingConfig()

            assert config.environment == 'PROD'
