"""
Comprehensive integration tests for Azure Blob Storage logging utilities.

This module tests the complete logging setup including Azure configuration,
fallback mechanisms, Loguru integration, and end-to-end logging workflows.
"""

import os
import threading
from unittest.mock import MagicMock, patch

from autolodge.log_utils.utils import _setup_local_file_logging, setup_logging


class TestLoggingSetupIntegration:
    """Integration tests for complete logging setup."""

    @patch('autolodge.log_utils.utils.check_azure_dependencies')
    @patch('autolodge.log_utils.utils.AzureLoggingConfig')
    @patch('autolodge.log_utils.utils.AzureBlobStorageSink')
    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_azure_success(self, mock_logger, mock_sink_class, mock_config_class, mock_check_deps):
        """Test successful Azure Blob Storage logging setup."""
        # Mock dependencies available
        mock_check_deps.return_value = True

        # Mock configuration
        mock_config = MagicMock()
        mock_config.is_configured.return_value = True
        mock_config.get_connection_string.return_value = 'test_connection_string'
        mock_config.container_name = 'test-container'
        mock_config.blob_prefix = 'test'
        mock_config.environment = 'TEST'
        mock_config_class.return_value = mock_config

        # Mock sink
        mock_sink = MagicMock()
        mock_sink.current_blob_name = 'test_TEST_2024-01-15.log'
        mock_sink_class.return_value = mock_sink

        result = setup_logging()

        # Verify Azure sink was created
        mock_sink_class.assert_called_once_with(
            connection_string='test_connection_string',
            container_name='test-container',
            blob_prefix='test',
            environment='TEST',
        )

        # Verify logger was configured
        mock_logger.add.assert_called()

        # Verify return value
        assert result == 'azure://test-container/test_TEST_2024-01-15.log'

    @patch('autolodge.log_utils.utils.check_azure_dependencies')
    @patch('autolodge.log_utils.utils.AzureLoggingConfig')
    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_azure_not_configured_fallback(self, mock_logger, mock_config_class, mock_check_deps):
        """Test fallback to local logging when Azure is not configured."""
        # Mock dependencies available but not configured
        mock_check_deps.return_value = True

        mock_config = MagicMock()
        mock_config.is_configured.return_value = False
        mock_config.environment = 'LOCAL'
        mock_config_class.return_value = mock_config

        with patch('autolodge.log_utils.utils._setup_local_file_logging') as mock_local_setup:
            mock_local_setup.return_value = '/path/to/local.log'

            result = setup_logging()

            # Verify local setup was called
            mock_local_setup.assert_called_once_with(True, mock_config)
            assert result == '/path/to/local.log'

    @patch('autolodge.log_utils.utils.check_azure_dependencies')
    @patch('autolodge.log_utils.utils.AzureLoggingConfig')
    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_azure_dependencies_unavailable(self, mock_logger, mock_config_class, mock_check_deps):
        """Test fallback when Azure dependencies are not available."""
        # Mock dependencies not available
        mock_check_deps.return_value = False

        mock_config = MagicMock()
        mock_config.environment = 'LOCAL'
        mock_config_class.return_value = mock_config

        with patch('autolodge.log_utils.utils._setup_local_file_logging') as mock_local_setup:
            mock_local_setup.return_value = '/path/to/local.log'

            result = setup_logging()

            # Verify local setup was called
            mock_local_setup.assert_called_once_with(False, mock_config)
            assert result == '/path/to/local.log'

    @patch('autolodge.log_utils.utils.check_azure_dependencies')
    @patch('autolodge.log_utils.utils.AzureLoggingConfig')
    @patch('autolodge.log_utils.utils.AzureBlobStorageSink')
    @patch('autolodge.log_utils.utils.logger')
    def test_setup_logging_azure_sink_creation_failure(
        self, mock_logger, mock_sink_class, mock_config_class, mock_check_deps
    ):
        """Test fallback when Azure sink creation fails."""
        # Mock dependencies available and configured
        mock_check_deps.return_value = True

        mock_config = MagicMock()
        mock_config.is_configured.return_value = True
        mock_config.get_connection_string.return_value = 'test_connection_string'
        mock_config.environment = 'TEST'
        mock_config_class.return_value = mock_config

        # Mock sink creation failure
        mock_sink_class.side_effect = Exception('Sink creation failed')

        with patch('autolodge.log_utils.utils._setup_local_file_logging') as mock_local_setup:
            mock_local_setup.return_value = '/path/to/local.log'

            result = setup_logging()

            # Verify error was logged and fallback occurred
            mock_logger.error.assert_called()
            mock_logger.warning.assert_called_with('⚠️ Falling back to local file logging')
            mock_local_setup.assert_called_once_with(True, mock_config)
            assert result == '/path/to/local.log'


class TestLocalFileLoggingFallback:
    """Tests for local file logging fallback functionality."""

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_local_file_logging_azure_unavailable(self, mock_logger):
        """Test local file logging setup when Azure is unavailable."""
        mock_config = MagicMock()
        mock_config.environment = 'TEST'

        with patch('autolodge.log_utils.utils.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            with patch('autolodge.log_utils.utils.Path') as mock_path:
                mock_logs_dir = MagicMock()
                mock_path.return_value.parent.parent.parent = MagicMock()
                mock_path.return_value.parent.parent.parent.__truediv__.return_value = mock_logs_dir

                result = _setup_local_file_logging(azure_available=False, azure_config=mock_config)

                # Verify warning was logged
                mock_logger.warning.assert_called_with('⚠️ Azure Blob Storage dependencies not available')

                # Verify logs directory creation
                mock_logs_dir.mkdir.assert_called_with(exist_ok=True)

                # Verify logger was configured
                mock_logger.add.assert_called()

    @patch('autolodge.log_utils.utils.logger')
    def test_setup_local_file_logging_azure_not_configured(self, mock_logger):
        """Test local file logging setup when Azure is not configured."""
        mock_config = MagicMock()
        mock_config.environment = 'TEST'
        mock_config.is_configured.return_value = False

        with patch('autolodge.log_utils.utils.datetime') as mock_datetime:
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            with patch('autolodge.log_utils.utils.Path') as mock_path:
                mock_logs_dir = MagicMock()
                mock_path.return_value.parent.parent.parent = MagicMock()
                mock_path.return_value.parent.parent.parent.__truediv__.return_value = mock_logs_dir

                result = _setup_local_file_logging(azure_available=True, azure_config=mock_config)

                # Verify configuration hints were logged
                mock_config.log_configuration_hints.assert_called_once()

    def test_local_file_logging_path_generation(self):
        """Test that local file paths are generated correctly."""
        mock_config = MagicMock()
        mock_config.environment = 'PROD'

        with (
            patch('autolodge.log_utils.utils.datetime') as mock_datetime,
            patch('autolodge.log_utils.utils.logger') as mock_logger,
        ):
            mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

            with patch('autolodge.log_utils.utils.Path') as mock_path:
                # Mock the path construction
                mock_file_path = MagicMock()
                mock_logs_dir = MagicMock()
                mock_logs_dir.__truediv__.return_value = mock_file_path
                mock_path.return_value.parent.parent.parent.__truediv__.return_value = mock_logs_dir

                result = _setup_local_file_logging(azure_available=True, azure_config=mock_config)

                # Verify the file path construction
                mock_logs_dir.__truediv__.assert_called_with('score_PROD_2024-01-15_10-30-45.log')


class TestEnvironmentSpecificLogging:
    """Tests for environment-specific logging behavior."""

    def test_production_environment_logging_setup(self, azure_env_vars):
        """Test logging setup for production environment."""
        with patch.dict(os.environ, {'DEPLOYMENT_ENVIRONMENT': 'PROD'}):
            with (
                patch('autolodge.log_utils.utils.check_azure_dependencies') as mock_check_deps,
                patch('autolodge.log_utils.utils.AzureBlobStorageSink') as mock_sink_class,
                patch('autolodge.log_utils.utils.logger') as mock_logger,
            ):
                mock_check_deps.return_value = True
                mock_sink = MagicMock()
                mock_sink.current_blob_name = 'score_PROD_2024-01-15.log'
                mock_sink_class.return_value = mock_sink

                result = setup_logging()

                # Verify production-specific configuration
                mock_sink_class.assert_called_once()
                call_args = mock_sink_class.call_args[1]
                assert call_args['environment'] == 'PROD'

    def test_development_environment_logging_setup(self, clean_env):
        """Test logging setup for development environment."""
        with patch.dict(os.environ, {'DEPLOYMENT_ENVIRONMENT': 'DEV'}):
            with (
                patch('autolodge.log_utils.utils.check_azure_dependencies') as mock_check_deps,
                patch('autolodge.log_utils.utils.AzureBlobStorageSink') as mock_sink_class,
                patch('autolodge.log_utils.utils.logger') as mock_logger,
            ):
                mock_check_deps.return_value = True
                mock_sink = MagicMock()
                mock_sink.current_blob_name = 'score_DEV_2024-01-15.log'
                mock_sink_class.return_value = mock_sink

                # Set up minimal Azure config
                with patch.dict(os.environ, {'AZURE_STORAGE_CONNECTION_STRING': 'test_connection_string'}):
                    result = setup_logging()

                    # Verify development-specific configuration
                    mock_sink_class.assert_called_once()
                    call_args = mock_sink_class.call_args[1]
                    assert call_args['environment'] == 'DEV'

    def test_unknown_environment_logging_setup(self, clean_env):
        """Test logging setup for unknown environment."""
        with (
            patch('autolodge.log_utils.utils.check_azure_dependencies') as mock_check_deps,
            patch('autolodge.log_utils.utils._setup_local_file_logging') as mock_local_setup,
        ):
            mock_check_deps.return_value = False
            mock_local_setup.return_value = '/path/to/local.log'

            result = setup_logging()

            # Verify fallback to local logging
            mock_local_setup.assert_called_once()
            config_arg = mock_local_setup.call_args[0][1]
            assert config_arg.environment == 'LOCAL'


class TestLoguruIntegration:
    """Tests for Loguru integration with Azure Blob Storage."""

    @patch('autolodge.log_utils.utils.check_azure_dependencies')
    @patch('autolodge.log_utils.utils.AzureLoggingConfig')
    @patch('autolodge.log_utils.utils.AzureBlobStorageSink')
    def test_loguru_handler_configuration(self, mock_sink_class, mock_config_class, mock_check_deps):
        """Test that Loguru handler is configured correctly for Azure."""
        mock_check_deps.return_value = True

        mock_config = MagicMock()
        mock_config.is_configured.return_value = True
        mock_config.get_connection_string.return_value = 'test_connection_string'
        mock_config_class.return_value = mock_config

        mock_sink = MagicMock()
        mock_sink_class.return_value = mock_sink

        with patch('autolodge.log_utils.utils.logger') as mock_logger:
            setup_logging()

            # Verify logger.add was called with correct parameters
            mock_logger.add.assert_called()
            call_args = mock_logger.add.call_args

            # Check sink parameter
            assert call_args[1]['sink'] == mock_sink.write

            # Check format parameter
            expected_format = '{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}'
            assert call_args[1]['format'] == expected_format

            # Check other parameters
            assert call_args[1]['level'] == 'DEBUG'
            assert call_args[1]['enqueue'] is True
            assert call_args[1]['backtrace'] is True
            assert call_args[1]['diagnose'] is True

    def test_loguru_format_consistency(self):
        """Test that log format is consistent between Azure and local logging."""
        expected_format = '{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}'

        # Test Azure format
        with (
            patch('autolodge.log_utils.utils.check_azure_dependencies') as mock_check_deps,
            patch('autolodge.log_utils.utils.AzureLoggingConfig') as mock_config_class,
            patch('autolodge.log_utils.utils.AzureBlobStorageSink') as mock_sink_class,
            patch('autolodge.log_utils.utils.logger') as mock_logger,
        ):
            mock_check_deps.return_value = True
            mock_config = MagicMock()
            mock_config.is_configured.return_value = True
            mock_config_class.return_value = mock_config
            mock_sink_class.return_value = MagicMock()

            setup_logging()

            azure_format = mock_logger.add.call_args[1]['format']
            assert azure_format == expected_format

        # Test local format
        with (
            patch('autolodge.log_utils.utils.check_azure_dependencies') as mock_check_deps,
            patch('autolodge.log_utils.utils.logger') as mock_logger,
        ):
            mock_check_deps.return_value = False

            with patch('autolodge.log_utils.utils.datetime') as mock_datetime, patch('autolodge.log_utils.utils.Path'):
                mock_datetime.now.return_value.strftime.return_value = '2024-01-15_10-30-45'

                setup_logging()

                local_format = mock_logger.add.call_args[1]['format']
                assert local_format == expected_format


class TestConcurrentLoggingScenarios:
    """Tests for concurrent logging scenarios."""

    def test_concurrent_setup_logging_calls(self, clean_env):
        """Test that concurrent setup_logging calls are handled safely."""
        results = []

        def setup_and_store():
            with (
                patch('autolodge.log_utils.utils.check_azure_dependencies') as mock_check_deps,
                patch('autolodge.log_utils.utils._setup_local_file_logging') as mock_local_setup,
            ):
                mock_check_deps.return_value = False
                mock_local_setup.return_value = '/path/to/local.log'

                result = setup_logging()
                results.append(result)

        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=setup_and_store)
            threads.append(thread)

        # Start all threads
        for thread in threads:
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify all calls completed successfully
        assert len(results) == 5
        assert all(result == '/path/to/local.log' for result in results)
