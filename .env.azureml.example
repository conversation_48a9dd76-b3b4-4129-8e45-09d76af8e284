# Azure ML Model Registry Configuration Example
# Copy this file to .env and configure your Azure ML details to enable model downloading from Azure ML Model Registry

# =============================================================================
# AZURE ML MODEL REGISTRY CONFIGURATION
# =============================================================================

# Azure ML Workspace Connection Details (Required for Azure ML mode)
AZURE_ML_SUBSCRIPTION_ID="your-azure-subscription-id"
AZURE_ML_RESOURCE_GROUP="your-resource-group-name"
AZURE_ML_WORKSPACE_NAME="your-azureml-workspace-name"

# Model Configuration (Required for Azure ML mode)
AZURE_ML_MODEL_NAME="your-model-name"
AZURE_ML_MODEL_VERSION="1"  # Optional - leave empty or remove for latest version

# Model Integrity Validation (Optional but recommended)
AZURE_ML_MODEL_MD5_HASH="your-model-md5-hash"  # Optional - for model integrity validation

# Local Caching Configuration (Optional)
AZURE_ML_CACHE_DIR="resources/cache"  # Optional - default: resources/cache

# Deployment Mode Control (Optional)
AZURE_ML_ENABLE="true"  # Set to "true" to enable Azure ML mode, "false" for local mode

# =============================================================================
# DEPLOYMENT ARCHITECTURE
# =============================================================================

# The system supports dual deployment architecture:
#
# 1. Azure ML Production Mode (AZURE_ML_ENABLE=true or auto-detected):
#    - Downloads models from Azure ML Model Registry
#    - Validates model integrity with MD5 hash
#    - Caches models locally for performance
#    - Auto-detects Azure environment (Azure ML compute instances)
#
# 2. Local Development Mode (AZURE_ML_ENABLE=false or not set):
#    - Uses local model files from resources/ directory
#    - Maintains existing local file paths and behavior
#    - No Azure ML dependencies required

# =============================================================================
# ENVIRONMENT DETECTION
# =============================================================================

# Azure ML mode is automatically enabled when:
# - AZURE_ML_ENABLE is set to "true", "1", "yes", or "on"
# - Running on Azure ML compute (auto-detected via environment variables)
# - AZUREML_RUN_ID, AZUREML_ARM_SUBSCRIPTION, or AML_PARAMETER_azureml_run_id are present

# =============================================================================
# AUTHENTICATION
# =============================================================================

# The system uses DefaultAzureCredential which supports multiple authentication methods:
# 1. Environment variables (service principal):
#    AZURE_CLIENT_ID=your-client-id
#    AZURE_CLIENT_SECRET=your-client-secret
#    AZURE_TENANT_ID=your-tenant-id
#
# 2. Managed identity (when running on Azure)
# 3. Azure CLI (for local development): az login
# 4. Visual Studio Code (for local development)
# 5. Interactive browser (fallback)

# =============================================================================
# USAGE EXAMPLES
# =============================================================================

# Example 1: Production deployment with specific model version
# AZURE_ML_SUBSCRIPTION_ID="12345678-1234-1234-1234-123456789012"
# AZURE_ML_RESOURCE_GROUP="rg-ml-prod"
# AZURE_ML_WORKSPACE_NAME="ml-workspace-prod"
# AZURE_ML_MODEL_NAME="autolodge-classifier"
# AZURE_ML_MODEL_VERSION="3"
# AZURE_ML_MODEL_MD5_HASH="a1b2c3d4e5f6789012345678901234567"
# AZURE_ML_ENABLE="true"

# Example 2: Development with latest model version
# AZURE_ML_SUBSCRIPTION_ID="12345678-1234-1234-1234-123456789012"
# AZURE_ML_RESOURCE_GROUP="rg-ml-dev"
# AZURE_ML_WORKSPACE_NAME="ml-workspace-dev"
# AZURE_ML_MODEL_NAME="autolodge-classifier-dev"
# # AZURE_ML_MODEL_VERSION not set = latest version
# AZURE_ML_ENABLE="true"

# Example 3: Local development mode (default)
# # No Azure ML variables set = local mode
# # Uses existing local files from resources/ directory

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# If you see "Missing required environment variables":
# - Make sure all required Azure ML variables are set
# - Check that your .env file is in the correct location
# - Verify variable names match exactly (case-sensitive)

# If you see "Azure ML client initialization failed":
# - Check your Azure credentials (run 'az login' for local development)
# - Verify subscription ID, resource group, and workspace name are correct
# - Ensure you have access to the Azure ML workspace

# If you see "Model not found in Azure ML Model Registry":
# - Verify the model name exists in your Azure ML workspace
# - Check if the specified version exists (or remove version for latest)
# - Ensure you have read access to the model registry

# If you see "Model validation failed - MD5 hash mismatch":
# - Verify the AZURE_ML_MODEL_MD5_HASH value is correct
# - Check if the model file was corrupted during download
# - Remove the cached model file and try again

# If you see "Model download failed":
# - Check network connectivity to Azure
# - Verify Azure ML workspace permissions
# - Check available disk space for model caching

# =============================================================================
# FEATURES
# =============================================================================

# ✅ Dual deployment architecture (Azure ML + Local)
# ✅ Automatic environment detection
# ✅ Model caching with version tracking
# ✅ MD5 hash validation for model integrity
# ✅ Comprehensive Loguru logging with emoji conventions
# ✅ Graceful fallback mechanisms
# ✅ Support for latest or specific model versions
# ✅ Compatible with existing local development workflow
# ✅ DefaultAzureCredential authentication
# ✅ Memory-efficient chunk-based MD5 calculation
# ✅ Thread-safe model downloading and caching

# =============================================================================
# INTEGRATION WITH EXISTING LOGGING
# =============================================================================

# This Azure ML integration works seamlessly with the existing Azure Blob Storage logging:
# - All Azure ML operations are logged with comprehensive details
# - Logs include model download progress, validation results, and caching status
# - Environment-specific log file naming is maintained
# - Emoji conventions match existing logging patterns
# - Error messages include actionable troubleshooting information

# To use both Azure ML model downloading AND Azure Blob Storage logging:
# 1. Configure Azure ML variables (this file)
# 2. Configure Azure Storage variables (.env.azure.example)
# 3. Both systems will work together automatically
